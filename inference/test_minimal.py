#!/usr/bin/env python3
"""
Minimal test for Phase 1 - focusing on configuration loading without transformers dependencies.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

print("=" * 60)
print("MINIMAL PHASE 1 TEST")
print("=" * 60)

# Test 1: Basic imports
print("Testing basic imports...")

try:
    import torch
    print("✓ PyTorch imported successfully")
    print(f"  - Version: {torch.__version__}")
    print(f"  - CUDA available: {torch.cuda.is_available()}")
except ImportError as e:
    print(f"✗ PyTorch import failed: {e}")
    sys.exit(1)

try:
    from omegaconf import DictConfig, OmegaConf
    print("✓ OmegaConf imported successfully")
except ImportError as e:
    print(f"✗ OmegaConf import failed: {e}")
    sys.exit(1)

# Test 2: Configuration loading without model dependencies
print("\nTesting configuration loading...")

try:
    from inference.config_loader import ConfigLoader
    print("✓ ConfigLoader imported successfully")
except ImportError as e:
    print(f"✗ ConfigLoader import failed: {e}")
    sys.exit(1)

try:
    config_path = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
    
    if not Path(config_path).exists():
        print(f"✗ Configuration file not found: {config_path}")
        sys.exit(1)
    
    config_loader = ConfigLoader()
    config = config_loader.load_config(config_path)
    print("✓ Configuration loaded successfully")
    
    # Test basic configuration access
    model_config = config_loader.get_model_config_dict()
    pretrained_name = config_loader.get_pretrained_model_name()
    tokenizer_name = config_loader.get_tokenizer_name()
    max_seq_len = config_loader.get_max_seq_len()
    
    print("✓ Configuration access methods working")
    print(f"  - Model config parameters: {len(model_config)}")
    print(f"  - Pretrained model: {pretrained_name}")
    print(f"  - Tokenizer: {tokenizer_name}")
    print(f"  - Max sequence length: {max_seq_len}")
    
    # Test specific configuration values
    print("\nConfiguration details:")
    print(f"  - Model name: {config_loader.model_config.get('name', 'unknown')}")
    print(f"  - Padding mode: {model_config.get('padding', 'unknown')}")
    print(f"  - Attention dropout: {model_config.get('attn_out_dropout_prob', 'unknown')}")
    print(f"  - Attention layer: {model_config.get('attention_layer', 'unknown')}")
    print(f"  - Hidden size: {model_config.get('hidden_size', 'unknown')}")
    print(f"  - Num layers: {model_config.get('num_hidden_layers', 'unknown')}")
    print(f"  - Sliding window: {model_config.get('sliding_window', 'unknown')}")
    print(f"  - Global attention every N layers: {model_config.get('global_attn_every_n_layers', 'unknown')}")
    
    # Validate unpadded configuration
    if model_config.get('padding') == 'unpadded':
        print("✓ Unpadded configuration detected")
    else:
        print(f"! Unexpected padding mode: {model_config.get('padding')}")
    
    # Validate attention dropout
    if model_config.get('attn_out_dropout_prob') == 0.1:
        print("✓ Attention dropout correctly set to 0.1")
    else:
        print(f"! Unexpected attention dropout: {model_config.get('attn_out_dropout_prob')}")
    
except Exception as e:
    print(f"✗ Configuration loading failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# Test 3: Check checkpoint file
print("\nTesting checkpoint file access...")

try:
    checkpoint_path = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-article-custom-tokenizer/ep3-ba339282-rank0.pt"
    
    if not Path(checkpoint_path).exists():
        print(f"✗ Checkpoint file not found: {checkpoint_path}")
        sys.exit(1)
    
    print("✓ Checkpoint file exists")
    
    # Check file size
    file_size = Path(checkpoint_path).stat().st_size
    print(f"  - File size: {file_size / (1024**3):.2f} GB")
    
    # Try to load checkpoint metadata (without loading the full model)
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    print("✓ Checkpoint loaded successfully")
    
    # Check checkpoint structure
    if 'state' in checkpoint:
        print("✓ Composer-style checkpoint detected")
        if 'model' in checkpoint['state']:
            model_keys = list(checkpoint['state']['model'].keys())[:5]  # First 5 keys
            print(f"  - Model state keys (first 5): {model_keys}")
        else:
            print("! No model state found in checkpoint")
    else:
        print("! Non-composer checkpoint format")
    
    # Clean up memory
    del checkpoint
    
except Exception as e:
    print(f"✗ Checkpoint loading failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n" + "=" * 60)
print("MINIMAL PHASE 1 TEST PASSED! ✓")
print("=" * 60)
print("Core functionality verified:")
print("  ✓ Configuration loading works")
print("  ✓ Checkpoint file accessible")
print("  ✓ Unpadded configuration detected")
print("  ✓ Attention dropout settings correct")
print("\nReady to proceed with model loading (requires fixing transformers dependency)")
