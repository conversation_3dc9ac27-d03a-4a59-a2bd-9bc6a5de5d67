#!/usr/bin/env python3
"""
Basic import test for Phase 1 components.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

print("Testing basic imports...")

try:
    import torch
    print("✓ PyTorch imported successfully")
    print(f"  - Version: {torch.__version__}")
    print(f"  - CUDA available: {torch.cuda.is_available()}")
except ImportError as e:
    print(f"✗ PyTorch import failed: {e}")
    sys.exit(1)

try:
    from omegaconf import DictConfig, OmegaConf
    print("✓ OmegaConf imported successfully")
except ImportError as e:
    print(f"✗ OmegaConf import failed: {e}")
    sys.exit(1)

try:
    from src.bert_layers.configuration_bert import FlexBertConfig
    print("✓ FlexBertConfig imported successfully")
except ImportError as e:
    print(f"✗ FlexBertConfig import failed: {e}")
    sys.exit(1)

try:
    from src.bert_layers.model import FlexBertForMaskedLM
    print("✓ FlexBertForMaskedLM imported successfully")
except ImportError as e:
    print(f"✗ FlexBertForMaskedLM import failed: {e}")
    sys.exit(1)

try:
    from inference.config_loader import ConfigLoader
    print("✓ ConfigLoader imported successfully")
except ImportError as e:
    print(f"✗ ConfigLoader import failed: {e}")
    sys.exit(1)

print("\nAll basic imports successful!")
print("Testing configuration loading...")

try:
    config_path = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
    config_loader = ConfigLoader()
    config = config_loader.load_config(config_path)
    print("✓ Configuration loaded successfully")
    
    # Test configuration validation
    config_loader.validate_config_for_inference()
    print("✓ Configuration validation passed")
    
    print("\nConfiguration details:")
    model_config = config_loader.get_model_config_dict()
    print(f"  - Padding mode: {model_config.get('padding', 'unknown')}")
    print(f"  - Attention dropout: {model_config.get('attn_out_dropout_prob', 'unknown')}")
    print(f"  - Hidden size: {model_config.get('hidden_size', 'unknown')}")
    print(f"  - Num layers: {model_config.get('num_hidden_layers', 'unknown')}")
    
except Exception as e:
    print(f"✗ Configuration loading failed: {e}")
    sys.exit(1)

print("\nBasic functionality test passed!")
