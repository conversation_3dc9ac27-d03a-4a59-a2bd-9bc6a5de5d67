# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
Model loading utilities for FlexBERT inference pipeline.
Handles model instantiation, checkpoint loading, and tokenizer setup.
"""

import os
import logging
import torch
from pathlib import Path
from typing import Dict, Any, Optional, Union, Tuple

# Import FlexBERT components
from src.bert_layers.configuration_bert import FlexBertConfig
from src.bert_layers.model import FlexBertForMaskedLM, FlexBertModel
from src.flex_bert import create_flex_bert_mlm

# Import transformers with error handling
try:
    import transformers
    from transformers import AutoTokenizer
    TRANSFORMERS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: transformers import failed: {e}")
    TRANSFORMERS_AVAILABLE = False
    AutoTokenizer = None

logger = logging.getLogger(__name__)


class ModelLoader:
    """
    Utility class for loading FlexBERT models and checkpoints.
    
    Handles:
    - Model instantiation from configuration
    - Checkpoint loading (.pt files)
    - Tokenizer loading
    - Device placement
    - Evaluation mode setup
    """
    
    def __init__(self, device: Optional[str] = None):
        """
        Initialize ModelLoader.
        
        Args:
            device: Target device ('cuda', 'cpu', or None for auto-detection)
        """
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.tokenizer = None
        self.config = None
        
        logger.info(f"ModelLoader initialized with device: {self.device}")
    
    def load_model_from_config(
        self,
        model_config_dict: Dict[str, Any],
        pretrained_model_name: str,
        tokenizer_name: Optional[str] = None
    ) -> Tuple[FlexBertForMaskedLM, Any]:
        """
        Load FlexBERT model from configuration dictionary.
        
        Args:
            model_config_dict: Model configuration parameters
            pretrained_model_name: Path to pretrained model
            tokenizer_name: Path to tokenizer (optional)
            
        Returns:
            Tuple of (model, tokenizer)
        """
        logger.info("Loading FlexBERT model from configuration...")
        
        try:
            # Create FlexBERT configuration
            self.config = FlexBertConfig.from_pretrained(
                pretrained_model_name, 
                **model_config_dict
            )
            
            # Log important configuration details
            logger.info(f"Model configuration:")
            logger.info(f"  - Hidden size: {self.config.hidden_size}")
            logger.info(f"  - Num layers: {self.config.num_hidden_layers}")
            logger.info(f"  - Num attention heads: {self.config.num_attention_heads}")
            logger.info(f"  - Padding mode: {self.config.padding}")
            logger.info(f"  - Attention layer: {self.config.attention_layer}")
            logger.info(f"  - Attention dropout: {self.config.attn_out_dropout_prob}")
            
            # Handle vocabulary size padding for divisibility by 8
            if self.config.vocab_size % 8 != 0:
                original_vocab_size = self.config.vocab_size
                self.config.vocab_size += 8 - (self.config.vocab_size % 8)
                logger.info(f"  - Vocab size padded: {original_vocab_size} -> {self.config.vocab_size}")
            
            # Create model
            self.model = FlexBertForMaskedLM(self.config)
            
            # Resize token embeddings if needed
            if hasattr(self.model, 'resize_token_embeddings'):
                self.model.resize_token_embeddings(self.config.vocab_size)
            
            # Move to device and set to evaluation mode
            self.model = self.model.to(self.device)
            self.model.eval()
            
            logger.info(f"Model loaded successfully on device: {self.device}")
            logger.info(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
            
            # Load tokenizer
            self.tokenizer = self._load_tokenizer(tokenizer_name or pretrained_model_name)
            
            return self.model, self.tokenizer
            
        except Exception as e:
            logger.error(f"Failed to load model from configuration: {e}")
            raise RuntimeError(f"Model loading failed: {e}")
    
    def load_checkpoint(self, checkpoint_path: Union[str, Path]) -> None:
        """
        Load model weights from checkpoint file.
        
        Args:
            checkpoint_path: Path to checkpoint (.pt file)
            
        Raises:
            FileNotFoundError: If checkpoint file doesn't exist
            RuntimeError: If checkpoint loading fails
        """
        if self.model is None:
            raise ValueError("Model must be loaded first. Call load_model_from_config().")
        
        checkpoint_path = Path(checkpoint_path)
        if not checkpoint_path.exists():
            raise FileNotFoundError(f"Checkpoint file not found: {checkpoint_path}")
        
        logger.info(f"Loading checkpoint from: {checkpoint_path}")
        
        try:
            # Load checkpoint
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            
            # Extract model state dict
            if 'state' in checkpoint:
                # Composer-style checkpoint
                state_dict = checkpoint['state'].get('model', {})
                logger.info("Loading from Composer-style checkpoint")
            elif 'model' in checkpoint:
                # Direct model state dict
                state_dict = checkpoint['model']
                logger.info("Loading from direct model checkpoint")
            else:
                # Assume the checkpoint is the state dict itself
                state_dict = checkpoint
                logger.info("Loading from raw state dict")
            
            if not state_dict:
                raise ValueError("No model state found in checkpoint")
            
            # Load state dict into model
            missing_keys, unexpected_keys = self.model.load_state_dict(state_dict, strict=False)
            
            if missing_keys:
                logger.warning(f"Missing keys in checkpoint: {missing_keys}")
            if unexpected_keys:
                logger.warning(f"Unexpected keys in checkpoint: {unexpected_keys}")
            
            # Ensure model is in evaluation mode
            self.model.eval()
            
            logger.info("Checkpoint loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load checkpoint: {e}")
            raise RuntimeError(f"Checkpoint loading failed: {e}")
    
    def _load_tokenizer(self, tokenizer_name: str):
        """
        Load tokenizer from path or name.

        Args:
            tokenizer_name: Path to tokenizer or HuggingFace model name

        Returns:
            Loaded tokenizer
        """
        if not TRANSFORMERS_AVAILABLE:
            raise RuntimeError("transformers library is not available")

        logger.info(f"Loading tokenizer from: {tokenizer_name}")

        try:
            tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
            logger.info(f"Tokenizer loaded successfully")
            logger.info(f"  - Vocab size: {tokenizer.vocab_size}")
            logger.info(f"  - Model max length: {tokenizer.model_max_length}")

            return tokenizer

        except Exception as e:
            logger.error(f"Failed to load tokenizer: {e}")
            raise RuntimeError(f"Tokenizer loading failed: {e}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded model.
        
        Returns:
            Dictionary with model information
        """
        if self.model is None:
            return {"status": "No model loaded"}
        
        info = {
            "status": "Model loaded",
            "device": str(self.device),
            "model_type": type(self.model).__name__,
            "parameters": sum(p.numel() for p in self.model.parameters()),
            "trainable_parameters": sum(p.numel() for p in self.model.parameters() if p.requires_grad),
            "is_training": self.model.training,
        }
        
        if self.config:
            info.update({
                "hidden_size": self.config.hidden_size,
                "num_layers": self.config.num_hidden_layers,
                "num_attention_heads": self.config.num_attention_heads,
                "vocab_size": self.config.vocab_size,
                "padding_mode": getattr(self.config, 'padding', 'unknown'),
                "attention_layer": getattr(self.config, 'attention_layer', 'unknown'),
            })
        
        if self.tokenizer:
            info.update({
                "tokenizer_vocab_size": self.tokenizer.vocab_size,
                "tokenizer_max_length": self.tokenizer.model_max_length,
            })
        
        return info
    
    def __repr__(self) -> str:
        status = "loaded" if self.model is not None else "not loaded"
        return f"ModelLoader(device={self.device}, model={status})"
