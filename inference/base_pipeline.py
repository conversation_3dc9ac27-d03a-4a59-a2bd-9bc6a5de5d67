# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
Base pipeline class for FlexBERT inference.
Provides common utilities for tokenization, padding/unpadding, and batch processing.
"""

import logging
import torch
from typing import Dict, List, Any, Optional, Union, Tuple
import torch.nn.functional as F

from src.bert_layers.model import FlexBertForMaskedLM
from src.bert_padding import unpad_input, pad_input
from .config_loader import ConfigLoader
from .model_loader import ModelLoader

# Import transformers with error handling
try:
    from transformers import AutoTokenizer
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    AutoTokenizer = None
    TRANSFORMERS_AVAILABLE = False

logger = logging.getLogger(__name__)


class BasePipeline:
    """
    Base class for FlexBERT inference pipelines.
    
    Provides common functionality:
    - Model and tokenizer management
    - Input preprocessing and tokenization
    - Padding/unpadding for unpadded attention models
    - Batch processing utilities
    - Device management
    """
    
    def __init__(
        self,
        model: FlexBertForMaskedLM,
        tokenizer: Any,
        config_loader: ConfigLoader,
        device: Optional[str] = None,
        max_length: Optional[int] = None
    ):
        """
        Initialize base pipeline.
        
        Args:
            model: Loaded FlexBERT model
            tokenizer: Loaded tokenizer
            config_loader: Configuration loader instance
            device: Target device
            max_length: Maximum sequence length (defaults to config value)
        """
        self.model = model
        self.tokenizer = tokenizer
        self.config_loader = config_loader
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self.max_length = max_length or config_loader.get_max_seq_len()
        
        # Get model configuration details
        self.model_config = config_loader.get_model_config_dict()
        self.is_unpadded = self.model_config.get('padding') == 'unpadded'
        
        logger.info(f"BasePipeline initialized:")
        logger.info(f"  - Device: {self.device}")
        logger.info(f"  - Max length: {self.max_length}")
        logger.info(f"  - Unpadded mode: {self.is_unpadded}")
        
        # Ensure model is in evaluation mode
        self.model.eval()
    
    def tokenize_texts(
        self,
        texts: Union[str, List[str]],
        add_special_tokens: bool = True,
        padding: Union[bool, str] = True,
        truncation: bool = True,
        return_tensors: str = "pt"
    ) -> Dict[str, torch.Tensor]:
        """
        Tokenize input texts.
        
        Args:
            texts: Input text(s) to tokenize
            add_special_tokens: Whether to add special tokens ([CLS], [SEP])
            padding: Padding strategy
            truncation: Whether to truncate long sequences
            return_tensors: Format of returned tensors
            
        Returns:
            Dictionary with tokenized inputs
        """
        if isinstance(texts, str):
            texts = [texts]
        
        # Tokenize
        encoded = self.tokenizer(
            texts,
            add_special_tokens=add_special_tokens,
            padding=padding,
            truncation=truncation,
            max_length=self.max_length,
            return_tensors=return_tensors
        )
        
        # Move to device
        encoded = {k: v.to(self.device) for k, v in encoded.items()}
        
        return encoded
    
    def prepare_inputs_for_unpadded_model(
        self,
        input_ids: torch.Tensor,
        attention_mask: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, int]:
        """
        Prepare inputs for unpadded attention models.
        
        Args:
            input_ids: Token IDs [batch_size, seq_len]
            attention_mask: Attention mask [batch_size, seq_len]
            
        Returns:
            Tuple of (unpadded_input_ids, indices, cu_seqlens, max_seqlen)
        """
        if not self.is_unpadded:
            return input_ids, None, None, None
        
        # Unpad the inputs
        unpadded_input_ids, indices, cu_seqlens, max_seqlen = unpad_input(
            input_ids, attention_mask
        )
        
        return unpadded_input_ids, indices, cu_seqlens, max_seqlen
    
    def process_batch(
        self,
        texts: List[str],
        batch_size: int = 8
    ) -> List[Dict[str, Any]]:
        """
        Process texts in batches.
        
        Args:
            texts: List of input texts
            batch_size: Batch size for processing
            
        Returns:
            List of results for each text
        """
        results = []
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_results = self._process_single_batch(batch_texts)
            results.extend(batch_results)
        
        return results
    
    def _process_single_batch(self, texts: List[str]) -> List[Dict[str, Any]]:
        """
        Process a single batch of texts.
        To be implemented by subclasses.
        
        Args:
            texts: Batch of input texts
            
        Returns:
            List of results for the batch
        """
        raise NotImplementedError("Subclasses must implement _process_single_batch")
    
    def get_hidden_states(
        self,
        input_ids: torch.Tensor,
        attention_mask: torch.Tensor,
        return_all_layers: bool = False
    ) -> torch.Tensor:
        """
        Get hidden states from the model.
        
        Args:
            input_ids: Token IDs
            attention_mask: Attention mask
            return_all_layers: Whether to return all layer outputs
            
        Returns:
            Hidden states tensor
        """
        with torch.no_grad():
            if self.is_unpadded:
                # Prepare inputs for unpadded model
                unpadded_ids, indices, cu_seqlens, max_seqlen = self.prepare_inputs_for_unpadded_model(
                    input_ids, attention_mask
                )
                
                # Get model outputs
                outputs = self.model.bert(
                    input_ids=unpadded_ids,
                    attention_mask=attention_mask,
                    indices=indices,
                    cu_seqlens=cu_seqlens,
                    max_seqlen=max_seqlen
                )
                
                # Pad outputs back if needed
                if indices is not None:
                    batch_size, seq_len = input_ids.shape
                    outputs = pad_input(outputs, indices, batch_size, seq_len)
            else:
                # Standard padded processing
                outputs = self.model.bert(
                    input_ids=input_ids,
                    attention_mask=attention_mask
                )
        
        return outputs
    
    def validate_inputs(self, texts: Union[str, List[str]]) -> List[str]:
        """
        Validate and normalize input texts.
        
        Args:
            texts: Input text(s)
            
        Returns:
            List of validated texts
            
        Raises:
            ValueError: If inputs are invalid
        """
        if isinstance(texts, str):
            texts = [texts]
        
        if not texts:
            raise ValueError("No input texts provided")
        
        # Check for empty texts
        valid_texts = []
        for i, text in enumerate(texts):
            if not isinstance(text, str):
                raise ValueError(f"Text at index {i} is not a string: {type(text)}")
            if not text.strip():
                logger.warning(f"Empty text at index {i}, skipping")
                continue
            valid_texts.append(text.strip())
        
        if not valid_texts:
            raise ValueError("No valid texts after filtering")
        
        return valid_texts
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """
        Get information about the pipeline.
        
        Returns:
            Dictionary with pipeline information
        """
        return {
            "pipeline_type": self.__class__.__name__,
            "device": self.device,
            "max_length": self.max_length,
            "is_unpadded": self.is_unpadded,
            "model_type": type(self.model).__name__,
            "tokenizer_type": type(self.tokenizer).__name__,
            "vocab_size": self.tokenizer.vocab_size,
        }
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(device={self.device}, unpadded={self.is_unpadded})"
