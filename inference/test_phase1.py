#!/usr/bin/env python3
# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
Test script for Phase 1 implementation.
Validates configuration loading, model loading, and basic functionality.
"""

import os
import sys
import logging
import torch
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from inference import Config<PERSON>oader, ModelLoader, BasePipeline

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_config_loading():
    """Test configuration loading functionality."""
    logger.info("=" * 60)
    logger.info("TESTING CONFIGURATION LOADING")
    logger.info("=" * 60)
    
    # Test with the specified YAML file
    config_path = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
    
    try:
        config_loader = ConfigLoader()
        config = config_loader.load_config(config_path)
        
        logger.info("✓ Configuration loaded successfully")
        
        # Validate configuration
        config_loader.validate_config_for_inference()
        logger.info("✓ Configuration validation passed")
        
        # Test configuration access methods
        model_config = config_loader.get_model_config_dict()
        pretrained_name = config_loader.get_pretrained_model_name()
        tokenizer_name = config_loader.get_tokenizer_name()
        max_seq_len = config_loader.get_max_seq_len()
        
        logger.info(f"✓ Model config extracted: {len(model_config)} parameters")
        logger.info(f"✓ Pretrained model name: {pretrained_name}")
        logger.info(f"✓ Tokenizer name: {tokenizer_name}")
        logger.info(f"✓ Max sequence length: {max_seq_len}")
        
        return config_loader
        
    except Exception as e:
        logger.error(f"✗ Configuration loading failed: {e}")
        raise


def test_model_loading(config_loader):
    """Test model loading functionality."""
    logger.info("=" * 60)
    logger.info("TESTING MODEL LOADING")
    logger.info("=" * 60)
    
    try:
        # Initialize model loader
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        model_loader = ModelLoader(device=device)
        
        logger.info(f"✓ ModelLoader initialized with device: {device}")
        
        # Load model from configuration
        model_config = config_loader.get_model_config_dict()
        pretrained_name = config_loader.get_pretrained_model_name()
        tokenizer_name = config_loader.get_tokenizer_name()
        
        model, tokenizer = model_loader.load_model_from_config(
            model_config_dict=model_config,
            pretrained_model_name=pretrained_name,
            tokenizer_name=tokenizer_name
        )
        
        logger.info("✓ Model loaded from configuration")
        
        # Get model info
        model_info = model_loader.get_model_info()
        logger.info("✓ Model information:")
        for key, value in model_info.items():
            logger.info(f"    {key}: {value}")
        
        return model_loader, model, tokenizer
        
    except Exception as e:
        logger.error(f"✗ Model loading failed: {e}")
        raise


def test_checkpoint_loading(model_loader):
    """Test checkpoint loading functionality."""
    logger.info("=" * 60)
    logger.info("TESTING CHECKPOINT LOADING")
    logger.info("=" * 60)
    
    checkpoint_path = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-article-custom-tokenizer/ep3-ba339282-rank0.pt"
    
    try:
        # Check if checkpoint exists
        if not Path(checkpoint_path).exists():
            logger.warning(f"Checkpoint file not found: {checkpoint_path}")
            logger.warning("Skipping checkpoint loading test")
            return
        
        # Load checkpoint
        model_loader.load_checkpoint(checkpoint_path)
        logger.info("✓ Checkpoint loaded successfully")
        
        # Verify model is still in eval mode
        if model_loader.model.training:
            logger.error("✗ Model is in training mode after checkpoint loading")
            raise RuntimeError("Model should be in evaluation mode")
        else:
            logger.info("✓ Model is in evaluation mode")
        
    except Exception as e:
        logger.error(f"✗ Checkpoint loading failed: {e}")
        raise


def test_basic_inference(model, tokenizer, config_loader):
    """Test basic inference functionality."""
    logger.info("=" * 60)
    logger.info("TESTING BASIC INFERENCE")
    logger.info("=" * 60)
    
    try:
        # Create a simple base pipeline instance
        pipeline = BasePipeline(
            model=model,
            tokenizer=tokenizer,
            config_loader=config_loader
        )
        
        logger.info("✓ BasePipeline created successfully")
        
        # Test tokenization
        test_text = "Hello, this is a test sentence for FlexBERT inference."
        encoded = pipeline.tokenize_texts(test_text)
        
        logger.info("✓ Text tokenization successful")
        logger.info(f"    Input shape: {encoded['input_ids'].shape}")
        logger.info(f"    Attention mask shape: {encoded['attention_mask'].shape}")
        
        # Test hidden states extraction
        hidden_states = pipeline.get_hidden_states(
            encoded['input_ids'],
            encoded['attention_mask']
        )
        
        logger.info("✓ Hidden states extraction successful")
        logger.info(f"    Hidden states shape: {hidden_states.shape}")
        
        # Test model forward pass (MLM)
        with torch.no_grad():
            if pipeline.is_unpadded:
                # Prepare inputs for unpadded model
                unpadded_ids, indices, cu_seqlens, max_seqlen = pipeline.prepare_inputs_for_unpadded_model(
                    encoded['input_ids'], encoded['attention_mask']
                )
                
                outputs = model(
                    input_ids=unpadded_ids,
                    attention_mask=encoded['attention_mask'],
                    indices=indices,
                    cu_seqlens=cu_seqlens,
                    max_seqlen=max_seqlen
                )
            else:
                outputs = model(
                    input_ids=encoded['input_ids'],
                    attention_mask=encoded['attention_mask']
                )
        
        logger.info("✓ Model forward pass successful")
        logger.info(f"    Logits shape: {outputs.logits.shape}")
        
        # Get pipeline info
        pipeline_info = pipeline.get_pipeline_info()
        logger.info("✓ Pipeline information:")
        for key, value in pipeline_info.items():
            logger.info(f"    {key}: {value}")
        
    except Exception as e:
        logger.error(f"✗ Basic inference test failed: {e}")
        raise


def main():
    """Run all Phase 1 tests."""
    logger.info("Starting Phase 1 Testing")
    logger.info("Testing FlexBERT Inference Pipeline - Phase 1 Implementation")
    
    try:
        # Test 1: Configuration Loading
        config_loader = test_config_loading()
        
        # Test 2: Model Loading
        model_loader, model, tokenizer = test_model_loading(config_loader)
        
        # Test 3: Checkpoint Loading
        test_checkpoint_loading(model_loader)
        
        # Test 4: Basic Inference
        test_basic_inference(model, tokenizer, config_loader)
        
        logger.info("=" * 60)
        logger.info("ALL PHASE 1 TESTS PASSED SUCCESSFULLY! ✓")
        logger.info("=" * 60)
        logger.info("Phase 1 implementation is ready for Phase 2 development.")
        
    except Exception as e:
        logger.error("=" * 60)
        logger.error(f"PHASE 1 TESTS FAILED: {e}")
        logger.error("=" * 60)
        sys.exit(1)


if __name__ == "__main__":
    main()
