# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
Configuration loading utilities for FlexBERT inference pipeline.
Handles YAML configuration files and model configuration parsing.
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union
import yaml

# Try to import OmegaConf, fall back to basic yaml if not available
try:
    from omegaconf import DictConfig, OmegaConf
    OMEGACONF_AVAILABLE = True
except ImportError:
    OMEGACONF_AVAILABLE = False
    DictConfig = dict  # Use regular dict as fallback

logger = logging.getLogger(__name__)


class ConfigLoader:
    """
    Utility class for loading and parsing YAML configuration files for FlexBERT models.
    
    Handles:
    - YAML file loading with variable substitution
    - Model configuration extraction
    - Tokenizer path resolution
    - Configuration validation
    """
    
    def __init__(self):
        self.config = None
        self.model_config = None
        self.tokenizer_name = None
        
    def load_config(self, config_path: Union[str, Path]) -> Union[DictConfig, Dict]:
        """
        Load configuration from YAML file.

        Args:
            config_path: Path to the YAML configuration file

        Returns:
            DictConfig or Dict: Loaded configuration

        Raises:
            FileNotFoundError: If config file doesn't exist
            ValueError: If config is invalid
        """
        config_path = Path(config_path)
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")

        logger.info(f"Loading configuration from: {config_path}")

        try:
            if OMEGACONF_AVAILABLE:
                # Load YAML with OmegaConf to handle variable substitution
                self.config = OmegaConf.load(config_path)
            else:
                # Fallback to basic yaml loading
                with open(config_path, 'r') as f:
                    self.config = yaml.safe_load(f)
                # Simple variable substitution for tokenizer_name
                if 'tokenizer_name' in self.config and isinstance(self.config['tokenizer_name'], str):
                    if '${tokenizer_name}' in str(self.config.get('model', {}).get('tokenizer_name', '')):
                        if hasattr(self.config['model'], 'tokenizer_name'):
                            self.config['model']['tokenizer_name'] = self.config['tokenizer_name']

            # Extract model configuration
            if 'model' not in self.config:
                raise ValueError("Configuration must contain 'model' section")

            self.model_config = self.config['model']

            # Extract tokenizer information
            self.tokenizer_name = self.config.get('tokenizer_name', None)
            if not self.tokenizer_name and 'tokenizer_name' in self.model_config:
                self.tokenizer_name = self.model_config['tokenizer_name']

            logger.info(f"Successfully loaded configuration for model: {self.model_config.get('name', 'unknown')}")
            logger.info(f"Tokenizer: {self.tokenizer_name}")

            return self.config

        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise ValueError(f"Invalid configuration file: {e}")
    
    def get_model_config_dict(self) -> Dict[str, Any]:
        """
        Get model configuration as a dictionary.
        
        Returns:
            Dict containing model configuration parameters
        """
        if self.model_config is None:
            raise ValueError("No configuration loaded. Call load_config() first.")
            
        # Convert DictConfig to regular dict if needed
        model_config = self.model_config.get('model_config', {})
        if OMEGACONF_AVAILABLE and hasattr(model_config, '__dict__'):
            return OmegaConf.to_container(model_config, resolve=True)
        else:
            return model_config
    
    def get_pretrained_model_name(self) -> str:
        """
        Get the pretrained model name/path.
        
        Returns:
            String path to pretrained model
        """
        if self.model_config is None:
            raise ValueError("No configuration loaded. Call load_config() first.")
            
        return self.model_config.get('pretrained_model_name', 'bert-base-uncased')
    
    def get_tokenizer_name(self) -> Optional[str]:
        """
        Get the tokenizer name/path.
        
        Returns:
            String path to tokenizer or None
        """
        return self.tokenizer_name
    
    def validate_config_for_inference(self) -> bool:
        """
        Validate that the configuration is suitable for inference.
        
        Returns:
            True if configuration is valid for inference
            
        Raises:
            ValueError: If configuration is invalid
        """
        if self.config is None:
            raise ValueError("No configuration loaded. Call load_config() first.")
        
        # Check required fields
        required_fields = ['model']
        for field in required_fields:
            if field not in self.config:
                raise ValueError(f"Missing required field in configuration: {field}")
        
        # Check model configuration
        model_config = self.get_model_config_dict()
        
        # Log important configuration details
        logger.info("Configuration validation:")
        logger.info(f"  - Model name: {self.model_config.get('name', 'unknown')}")
        logger.info(f"  - Padding mode: {model_config.get('padding', 'unknown')}")
        logger.info(f"  - Attention dropout: {model_config.get('attn_out_dropout_prob', 'unknown')}")
        logger.info(f"  - Attention layer: {model_config.get('attention_layer', 'unknown')}")
        logger.info(f"  - Hidden size: {model_config.get('hidden_size', 'unknown')}")
        logger.info(f"  - Num layers: {model_config.get('num_hidden_layers', 'unknown')}")
        
        # Warn about unpadded configuration
        if model_config.get('padding') == 'unpadded':
            logger.info("  - Using unpadded attention (optimized for variable-length sequences)")
        
        return True
    
    def get_max_seq_len(self) -> int:
        """
        Get maximum sequence length from configuration.
        
        Returns:
            Maximum sequence length
        """
        return self.config.get('max_seq_len', 512)
    
    def __repr__(self) -> str:
        if self.config is None:
            return "ConfigLoader(no config loaded)"
        return f"ConfigLoader(model={self.model_config.get('name', 'unknown')}, tokenizer={self.tokenizer_name})"
