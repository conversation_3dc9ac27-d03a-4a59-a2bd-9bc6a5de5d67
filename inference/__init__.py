# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
FlexBERT Inference Pipeline

This package provides inference utilities for FlexBERT models, supporting:
- MLM (Masked Language Modeling) mask-filling tasks
- Sentence embedding generation
- Efficient processing with unpadded attention
"""

from .config_loader import ConfigLoader
from .model_loader import ModelLoader
from .base_pipeline import BasePipeline

__version__ = "0.1.0"

__all__ = [
    "ConfigLoader",
    "ModelLoader", 
    "BasePipeline",
]
